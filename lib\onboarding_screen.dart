import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:panchang_at_this_moment/main.dart';
import 'package:panchang_at_this_moment/home_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int _currentStep = 0;
  String? _selectedLanguage;
  bool _isLoadingLocation = false;
  String _locationStatus = '';

  final Map<String, String> _languages = {
    'en': 'English',
    'hi': 'हिन्दी',
    'te': 'తెలుగు',
    'ta': 'தமிழ்',
    'kn': 'ಕನ್ನಡ',
    'ml': 'മലയാളം',
    'be': 'বাংলা',
    'mr': 'मराठी',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF3E5AB), // Warm cream color
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // App Logo and Title
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/panchang_logo.jpg',
                        width: 120,
                        height: 120,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Panchang at this Moment',
                        style: Theme.of(context).textTheme.displayLarge?.copyWith(
                          color: const Color(0xFF6B4E3D),
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'Welcome! Let\'s set up your app',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: const Color(0xFF8D6E63),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Content Area
                Expanded(
                  flex: 3,
                  child: _currentStep == 0 ? _buildLanguageSelection() : _buildLocationSetup(),
                ),

                // Navigation Buttons
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: Row(
                    children: [
                      if (_currentStep > 0)
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              setState(() {
                                _currentStep = 0;
                              });
                            },
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              side: const BorderSide(color: Color(0xFF6B4E3D)),
                            ),
                            child: Text(
                              'Back',
                              style: TextStyle(
                                fontSize: 18,
                                color: const Color(0xFF6B4E3D),
                              ),
                            ),
                          ),
                        ),
                      if (_currentStep > 0) const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _canProceed() ? _handleNext : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF6B4E3D),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            disabledBackgroundColor: Colors.grey[300],
                          ),
                          child: Text(
                            _currentStep == 0 ? 'Next' : 'Get Started',
                            style: const TextStyle(fontSize: 18),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose your preferred language',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: const Color(0xFF3E2723),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 20),
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _languages.length,
            itemBuilder: (context, index) {
              final entry = _languages.entries.elementAt(index);
              final isSelected = _selectedLanguage == entry.key;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedLanguage = entry.key;
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFF6B4E3D) : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? const Color(0xFF6B4E3D) : const Color(0xFFE0E0E0),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      entry.value,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: isSelected ? Colors.white : const Color(0xFF3E2723),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLocationSetup() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location Permission',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: const Color(0xFF3E2723),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Icon(
                Icons.location_on_rounded,
                size: 64,
                color: const Color(0xFF6B4E3D),
              ),
              const SizedBox(height: 16),
              Text(
                'We need your location to provide accurate Panchang data for your area.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: const Color(0xFF5D4037),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              if (_locationStatus.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF3E5AB),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _locationStatus,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF6B4E3D),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              const SizedBox(height: 20),
              if (_isLoadingLocation)
                const CircularProgressIndicator(
                  color: Color(0xFF6B4E3D),
                )
              else
                ElevatedButton.icon(
                  onPressed: _requestLocationPermission,
                  icon: const Icon(Icons.my_location),
                  label: const Text('Allow Location Access'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6B4E3D),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
            ],
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFFF3E0),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFFFB74D), width: 1),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: const Color(0xFFFF8F00),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'If you skip this step, we\'ll use Indore, India as the default location.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFFE65100),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool _canProceed() {
    if (_currentStep == 0) {
      return _selectedLanguage != null;
    } else {
      return true; // Can always proceed from location step
    }
  }

  void _handleNext() {
    if (_currentStep == 0) {
      // Set the selected language
      if (_selectedLanguage != null) {
        MyApp.of(context)?.setLocale(Locale(_selectedLanguage!));
      }
      setState(() {
        _currentStep = 1;
      });
    } else {
      // Complete onboarding
      _completeOnboarding();
    }
  }

  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoadingLocation = true;
      _locationStatus = 'Requesting location permission...';
    });

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _locationStatus = 'Location services are disabled. Using default location (Indore).';
          _isLoadingLocation = false;
        });
        return;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _locationStatus = 'Location permission denied. Using default location (Indore).';
            _isLoadingLocation = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _locationStatus = 'Location permission permanently denied. Using default location (Indore).';
          _isLoadingLocation = false;
        });
        return;
      }

      // Get current position
      setState(() {
        _locationStatus = 'Getting your location...';
      });

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // Save location to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('lat', position.latitude);
      await prefs.setDouble('lng', position.longitude);

      setState(() {
        _locationStatus = 'Location saved successfully!';
        _isLoadingLocation = false;
      });

    } catch (e) {
      setState(() {
        _locationStatus = 'Could not get location. Using default location (Indore).';
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);

    // Navigate to home screen
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomeScreen()),
      );
    }
  }
}
