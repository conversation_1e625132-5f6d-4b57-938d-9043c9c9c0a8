import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SunMoonTimeRow extends StatelessWidget {
  final IconData icon;
  final String title;
  final DateTime? rise;
  final DateTime? set;

  const SunMoonTimeRow(
      {super.key,
      required this.icon,
      required this.title,
      this.rise,
      this.set});

  Widget _buildTime(String label, DateTime? time, Color iconColor, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                fontSize: 14
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          time == null
              ? const SizedBox(
                  width: 12,
                  height: 12,
                  child: CircularProgressIndicator(strokeWidth: 1.5),
                )
              : Expanded(
                  flex: 3,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        label.toLowerCase().contains('rise') ? Icons.arrow_upward : Icons.arrow_downward,
                        size: 12,
                        color: iconColor,
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          timeFormatter(time),
                          style: Theme.of(context).textTheme.headlineMedium,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Color iconColor = title == 'Sun' ? Colors.orange : Colors.indigo;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 0),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: title == 'Sun'
              ? [const Color(0xFFFFF8E1), const Color(0xFFFFE0B2)]
              : [const Color(0xFFE8EAF6), const Color(0xFFC5CAE9)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: iconColor,
              ),
              const SizedBox(width: 8),
              Text(
                title == 'Sun' ? 'Sun' : 'Moon',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: iconColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Column(
            children: [
              _buildTime(
                title=='Sun' ? AppLocalizations.of(context)!.sunrise : AppLocalizations.of(context)!.moonRise,
                rise,
                iconColor,
                context,
              ),
              const SizedBox(height: 6),
              _buildTime(
                title=='Sun' ? AppLocalizations.of(context)!.sunset : AppLocalizations.of(context)!.moonSet,
                set,
                iconColor,
                context,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

List<Widget> getStaticPanchangItem(PanchangDataForThreeDays? panchangData, DateTime currentTime, BuildContext context) {
  if (panchangData == null) {
    return const [
      Center(child: CircularProgressIndicator()),
    ];
  }

  // The logic remains exactly the same as before
  DateTime? sunrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.sunrise.isAfter(currentTime) &&
        sunrise == null) {
      sunrise = dayData.sunrise;
      break;
    }
  }

  DateTime? sunset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (sunrise != null && dayData.sunset.isAfter(sunrise)) {
      sunset = dayData.sunset;
      break;
    }
  }

  DateTime? moonrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.moonrise.isAfter(currentTime.add(const Duration(hours: 3))) &&
        moonrise == null) {
      moonrise = dayData.moonrise;
      break;
    }
  }

  DateTime? moonset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (moonrise != null && dayData.moonset.isAfter(moonrise)) {
      moonset = dayData.moonset;
      break;
    }
  }

  return [
    SunMoonTimeRow(
      title: 'Sun',
      icon: Icons.wb_sunny,
      rise: sunrise,
      set: sunset,
    ),
    SunMoonTimeRow(
      title: 'Moon',
      icon: Icons.nightlight_round,
      rise: moonrise,
      set: moonset,
    ),
    Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.brown.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(Icons.calendar_month, color: Colors.brown, size: 20),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppLocalizations.of(context)!.week,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    panchangData.currentDay.weekday,
                    style: Theme.of(context).textTheme.headlineMedium,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  ];
}