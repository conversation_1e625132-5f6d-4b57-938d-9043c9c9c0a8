import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:panchang_at_this_moment/components/panchang_info_card.dart';
import 'package:panchang_at_this_moment/components/panchang_nakshatra_card.dart';

List<Widget> getPanchangItemAlwayChange({
  required PanchangDataForThreeDays? panchangData,
  required DateTime currentTime,
  required BuildContext context
}) {
  final PanchangAlwaysChangeItem currentNakshatra =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.nakshatraList ?? [],
  panchangData?.currentDay.nakshatraList ?? [],
  panchangData?.nextDay.nakshatraList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentKarana =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.karanaList ?? [],
  panchangData?.currentDay.karanaList ?? [],
  panchangData?.nextDay.karanaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentTithiPaksha =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.tithiPakshaList ?? [],
  panchangData?.currentDay.tithiPakshaList ?? [],
  panchangData?.nextDay.tithiPakshaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentNakshatraPada =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.nakshatraPadaList ?? [],
  panchangData?.currentDay.nakshatraPadaList ?? [],
  panchangData?.nextDay.nakshatraPadaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentYoga =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.yogaList ?? [],
  panchangData?.currentDay.yogaList ?? [],
  panchangData?.nextDay.yogaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentSuryaNakshatra =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.suryaNakshatraList ?? [],
  panchangData?.currentDay.suryaNakshatraList ?? [],
  panchangData?.nextDay.suryaNakshatraList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentSuryaPada =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.suryaPadaList ?? [],
  panchangData?.currentDay.suryaPadaList ?? [],
  panchangData?.nextDay.suryaPadaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentLunarRaasi =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.lunarRaasiList ?? [],
  panchangData?.currentDay.lunarRaasiList ?? [],
  panchangData?.nextDay.lunarRaasiList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentSolarRaasi =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.solarRaasiList ?? [],
  panchangData?.currentDay.solarRaasiList ?? [],
  panchangData?.nextDay.solarRaasiList ?? []
].expand((x) => x).toList());

  final String maasa = panchangData?.currentDay.maasa ?? '';
  final String samvatsara = panchangData?.currentDay.samvatsara ?? '';

  return [
    // Tithi Paksha Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: currentTithiPaksha.name,
          startTime: currentTithiPaksha.start,
          endTime: currentTithiPaksha.end,
        )
      ],
      child: PanchangInfoCard(
        color: Colors.orange,
        icon: Icons.calendar_today,
        title: AppLocalizations.of(context)!.tithiPaksha,
        value: currentTithiPaksha.name,
        context: context,
      ),
    ),
    
    // Karana Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.karana} : ${currentKarana.name}",
          startTime: currentKarana.start,
          endTime: currentKarana.end,
        )
      ],
      child: PanchangInfoCard(
        color: Colors.indigo,
        icon: Icons.access_time,
        title: AppLocalizations.of(context)!.karana,
        value: currentKarana.name,
        context: context,
      ),
    ),
    
    // Yoga Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.yoga} : ${currentYoga.name}",
          startTime: currentYoga.start,
          endTime: currentYoga.end,
        )
      ],
      child: PanchangInfoCard(
        color: Colors.teal,
        icon: Icons.autorenew,
        title: AppLocalizations.of(context)!.yoga,
        value: currentYoga.name,
        context: context,
      ),
    ),
    
    // Maasa Card
    Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: PanchangInfoCard(
        color: Colors.amber,
        icon: Icons.date_range,
        title: AppLocalizations.of(context)!.maasa,
        value: maasa,
        context: context,
      ),
    ),
    
    // Samvatsara Card
    Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: PanchangInfoCard(
        color: Colors.purple,
        icon: Icons.event,
        title: AppLocalizations.of(context)!.samvatsara,
        value: samvatsara,
        context: context,
      ),
    ),
    
    // Nakshatra Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.nakshatra} : ${currentNakshatra.name}",
          startTime: currentNakshatra.start,
          endTime: currentNakshatra.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.nakshatraPada} : ${currentNakshatraPada.name}",
          startTime: currentNakshatraPada.start,
          endTime: currentNakshatraPada.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.rashi} : ${currentLunarRaasi.name}",
          startTime: currentLunarRaasi.start,
          endTime: currentLunarRaasi.end,
        )
      ],
      child: PanchangNakshatraCard(
        color: Colors.blueGrey,
        icon: Icons.nightlight_round,
        title: AppLocalizations.of(context)!.nakshatra,
        infoRows: [
          {
            'label': AppLocalizations.of(context)!.nakshatra,
            'value': currentNakshatra.name,
          },
          {
            'label': AppLocalizations.of(context)!.nakshatraPada,
            'value': currentNakshatraPada.name,
          },
          {
            'label': AppLocalizations.of(context)!.rashi,
            'value': currentLunarRaasi.name,
          },
        ],
        infoBgColor: Colors.blue.withOpacity(0.05),
      ),
    ),
    
    // Surya Nakshatra Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      showDateFormattedTime: true,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.suryaNakshatra} : ${currentSuryaNakshatra.name}",
          startTime: currentSuryaNakshatra.start,
          endTime: currentSuryaNakshatra.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.suryaNakshatraPada} : ${currentSuryaPada.name}",
          startTime: currentSuryaPada.start,
          endTime: currentSuryaPada.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.suryaRashi} : ${currentSolarRaasi.name}",
          startTime: currentSolarRaasi.start,
          endTime: currentSolarRaasi.end,
        )
      ],
      child: PanchangNakshatraCard(
        color: Colors.orange,
        icon: Icons.wb_sunny,
        title: AppLocalizations.of(context)!.suryaNakshatra,
        infoRows: [
          {
            'label': AppLocalizations.of(context)!.suryaNakshatra,
            'value': currentSuryaNakshatra.name,
          },
          {
            'label': AppLocalizations.of(context)!.suryaNakshatraPada,
            'value': currentSuryaPada.name,
          },
          {
            'label': AppLocalizations.of(context)!.suryaRashi,
            'value': currentSolarRaasi.name,
          },
        ],
        infoBgColor: Colors.orange.withOpacity(0.05),
      ),
    ),
  ];
}


PanchangAlwaysChangeItem _getCurrentNakshatra(
    DateTime currentTime, List<PanchangAlwaysChangeItem> nakshatraList) {
  return nakshatraList.firstWhere(
      (element) =>
          element.start.isBefore(currentTime) &&
          element.end.isAfter(currentTime),
      orElse: () => PanchangAlwaysChangeItem(
            name: 'Failed to get data',
            start: DateTime.now(),
            end: DateTime.now(),
          ));
}